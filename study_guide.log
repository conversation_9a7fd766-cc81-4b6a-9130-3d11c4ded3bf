This is pdfTeX, Version 3.141592653-2.6-1.40.27 (TeX Live 2025) (preloaded format=pdflatex 2025.5.23)  25 MAY 2025 15:49
entering extended mode
 restricted \write18 enabled.
 %&-line parsing enabled.
**study_guide.tex
(./study_guide.tex
LaTeX2e <2024-11-01> patch level 2
L3 programming layer <2025-01-18>
(/usr/local/texlive/2025basic/texmf-dist/tex/latex/base/article.cls
Document Class: article 2024/06/29 v1.4n Standard LaTeX document class
(/usr/local/texlive/2025basic/texmf-dist/tex/latex/base/size10.clo
File: size10.clo 2024/06/29 v1.4n Standard LaTeX file (size option)
)
\c@part=\count196
\c@section=\count197
\c@subsection=\count198
\c@subsubsection=\count199
\c@paragraph=\count266
\c@subparagraph=\count267
\c@figure=\count268
\c@table=\count269
\abovecaptionskip=\skip49
\belowcaptionskip=\skip50
\bibindent=\dimen141
)
(/usr/local/texlive/2025basic/texmf-dist/tex/latex/geometry/geometry.sty
Package: geometry 2020/01/02 v5.9 Page Geometry

(/usr/local/texlive/2025basic/texmf-dist/tex/latex/graphics/keyval.sty
Package: keyval 2022/05/29 v1.15 key=value parser (DPC)
\KV@toks@=\toks17
)
(/usr/local/texlive/2025basic/texmf-dist/tex/generic/iftex/ifvtex.sty
Package: ifvtex 2019/10/25 v1.7 ifvtex legacy package. Use iftex instead.

(/usr/local/texlive/2025basic/texmf-dist/tex/generic/iftex/iftex.sty
Package: iftex 2024/12/12 v1.0g TeX engine tests
))
\Gm@cnth=\count270
\Gm@cntv=\count271
\c@Gm@tempcnt=\count272
\Gm@bindingoffset=\dimen142
\Gm@wd@mp=\dimen143
\Gm@odd@mp=\dimen144
\Gm@even@mp=\dimen145
\Gm@layoutwidth=\dimen146
\Gm@layoutheight=\dimen147
\Gm@layouthoffset=\dimen148
\Gm@layoutvoffset=\dimen149
\Gm@dimlist=\toks18
)
(/usr/local/texlive/2025basic/texmf-dist/tex/latex/tools/multicol.sty
Package: multicol 2024/09/14 v1.9i multicolumn formatting (FMi)
\c@tracingmulticols=\count273
\mult@box=\box52
\multicol@leftmargin=\dimen150
\c@unbalance=\count274
\c@collectmore=\count275
\doublecol@number=\count276
\multicoltolerance=\count277
\multicolpretolerance=\count278
\full@width=\dimen151
\page@free=\dimen152
\premulticols=\dimen153
\postmulticols=\dimen154
\multicolsep=\skip51
\multicolbaselineskip=\skip52
\partial@page=\box53
\last@line=\box54
\mc@boxedresult=\box55
\maxbalancingoverflow=\dimen155
\mult@rightbox=\box56
\mult@grightbox=\box57
\mult@firstbox=\box58
\mult@gfirstbox=\box59
\@tempa=\box60
\@tempa=\box61
\@tempa=\box62
\@tempa=\box63
\@tempa=\box64
\@tempa=\box65
\@tempa=\box66
\@tempa=\box67
\@tempa=\box68
\@tempa=\box69
\@tempa=\box70
\@tempa=\box71
\@tempa=\box72
\@tempa=\box73
\@tempa=\box74
\@tempa=\box75
\@tempa=\box76
\@tempa=\box77
\@tempa=\box78
\@tempa=\box79
\@tempa=\box80
\@tempa=\box81
\@tempa=\box82
\@tempa=\box83
\@tempa=\box84
\@tempa=\box85
\@tempa=\box86
\@tempa=\box87
\@tempa=\box88
\@tempa=\box89
\@tempa=\box90
\@tempa=\box91
\@tempa=\box92
\@tempa=\box93
\@tempa=\box94
\@tempa=\box95
\c@minrows=\count279
\c@columnbadness=\count280
\c@finalcolumnbadness=\count281
\last@try=\dimen156
\multicolovershoot=\dimen157
\multicolundershoot=\dimen158
\mult@nat@firstbox=\box96
\colbreak@box=\box97
\mc@col@check@num=\count282
)
(/usr/local/texlive/2025basic/texmf-dist/tex/latex/enumitem/enumitem.sty
Package: enumitem 2025/02/06 v3.11 Customized lists
\labelindent=\skip53
\enit@outerparindent=\dimen159
\enit@toks=\toks19
\enit@inbox=\box98
\enit@count@id=\count283
\enitdp@description=\count284
)
(/usr/local/texlive/2025basic/texmf-dist/tex/latex/xcolor/xcolor.sty
Package: xcolor 2024/09/29 v3.02 LaTeX color extensions (UK)

(/usr/local/texlive/2025basic/texmf-dist/tex/latex/graphics-cfg/color.cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package xcolor Info: Driver file: pdftex.def on input line 274.

(/usr/local/texlive/2025basic/texmf-dist/tex/latex/graphics-def/pdftex.def
File: pdftex.def 2024/04/13 v1.2c Graphics/color driver for pdftex
)
(/usr/local/texlive/2025basic/texmf-dist/tex/latex/graphics/mathcolor.ltx)
Package xcolor Info: Model `cmy' substituted by `cmy0' on input line 1349.
Package xcolor Info: Model `hsb' substituted by `rgb' on input line 1353.
Package xcolor Info: Model `RGB' extended on input line 1365.
Package xcolor Info: Model `HTML' substituted by `rgb' on input line 1367.
Package xcolor Info: Model `Hsb' substituted by `hsb' on input line 1368.
Package xcolor Info: Model `tHsb' substituted by `hsb' on input line 1369.
Package xcolor Info: Model `HSB' substituted by `hsb' on input line 1370.
Package xcolor Info: Model `Gray' substituted by `gray' on input line 1371.
Package xcolor Info: Model `wave' substituted by `hsb' on input line 1372.
)
(/usr/local/texlive/2025basic/texmf-dist/tex/latex/tcolorbox/tcolorbox.sty
Package: tcolorbox 2025/05/20 version 6.5.0 text color boxes

(/usr/local/texlive/2025basic/texmf-dist/tex/latex/pgf/frontendlayer/tikz.sty
(/usr/local/texlive/2025basic/texmf-dist/tex/latex/pgf/basiclayer/pgf.sty
(/usr/local/texlive/2025basic/texmf-dist/tex/latex/pgf/utilities/pgfrcs.sty
(/usr/local/texlive/2025basic/texmf-dist/tex/generic/pgf/utilities/pgfutil-comm
on.tex
\pgfutil@everybye=\toks20
\pgfutil@tempdima=\dimen160
\pgfutil@tempdimb=\dimen161
)
(/usr/local/texlive/2025basic/texmf-dist/tex/generic/pgf/utilities/pgfutil-late
x.def
\pgfutil@abb=\box99
)
(/usr/local/texlive/2025basic/texmf-dist/tex/generic/pgf/utilities/pgfrcs.code.
tex (/usr/local/texlive/2025basic/texmf-dist/tex/generic/pgf/pgf.revision.tex)
Package: pgfrcs 2023-01-15 v3.1.10 (3.1.10)
))
Package: pgf 2023-01-15 v3.1.10 (3.1.10)
 (/usr/local/texlive/2025basic/texmf-dist/tex/latex/pgf/basiclayer/pgfcore.sty 
(/usr/local/texlive/2025basic/texmf-dist/tex/latex/graphics/graphicx.sty
Package: graphicx 2021/09/16 v1.2d Enhanced LaTeX Graphics (DPC,SPQR)

(/usr/local/texlive/2025basic/texmf-dist/tex/latex/graphics/graphics.sty
Package: graphics 2024/08/06 v1.4g Standard LaTeX Graphics (DPC,SPQR)

(/usr/local/texlive/2025basic/texmf-dist/tex/latex/graphics/trig.sty
Package: trig 2023/12/02 v1.11 sin cos tan (DPC)
)
(/usr/local/texlive/2025basic/texmf-dist/tex/latex/graphics-cfg/graphics.cfg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: pdftex.def on input line 106.
)
\Gin@req@height=\dimen162
\Gin@req@width=\dimen163
) (/usr/local/texlive/2025basic/texmf-dist/tex/latex/pgf/systemlayer/pgfsys.sty

(/usr/local/texlive/2025basic/texmf-dist/tex/generic/pgf/systemlayer/pgfsys.cod
e.tex
Package: pgfsys 2023-01-15 v3.1.10 (3.1.10)

(/usr/local/texlive/2025basic/texmf-dist/tex/generic/pgf/utilities/pgfkeys.code
.tex
\pgfkeys@pathtoks=\toks21
\pgfkeys@temptoks=\toks22

(/usr/local/texlive/2025basic/texmf-dist/tex/generic/pgf/utilities/pgfkeyslibra
ryfiltered.code.tex
\pgfkeys@tmptoks=\toks23
))
\pgf@x=\dimen164
\pgf@y=\dimen165
\pgf@xa=\dimen166
\pgf@ya=\dimen167
\pgf@xb=\dimen168
\pgf@yb=\dimen169
\pgf@xc=\dimen170
\pgf@yc=\dimen171
\pgf@xd=\dimen172
\pgf@yd=\dimen173
\w@pgf@writea=\write3
\r@pgf@reada=\read2
\c@pgf@counta=\count285
\c@pgf@countb=\count286
\c@pgf@countc=\count287
\c@pgf@countd=\count288
\t@pgf@toka=\toks24
\t@pgf@tokb=\toks25
\t@pgf@tokc=\toks26
\pgf@sys@id@count=\count289

(/usr/local/texlive/2025basic/texmf-dist/tex/generic/pgf/systemlayer/pgf.cfg
File: pgf.cfg 2023-01-15 v3.1.10 (3.1.10)
)
Driver file for pgf: pgfsys-pdftex.def

(/usr/local/texlive/2025basic/texmf-dist/tex/generic/pgf/systemlayer/pgfsys-pdf
tex.def
File: pgfsys-pdftex.def 2023-01-15 v3.1.10 (3.1.10)

(/usr/local/texlive/2025basic/texmf-dist/tex/generic/pgf/systemlayer/pgfsys-com
mon-pdf.def
File: pgfsys-common-pdf.def 2023-01-15 v3.1.10 (3.1.10)
)))
(/usr/local/texlive/2025basic/texmf-dist/tex/generic/pgf/systemlayer/pgfsyssoft
path.code.tex
File: pgfsyssoftpath.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfsyssoftpath@smallbuffer@items=\count290
\pgfsyssoftpath@bigbuffer@items=\count291
)
(/usr/local/texlive/2025basic/texmf-dist/tex/generic/pgf/systemlayer/pgfsysprot
ocol.code.tex
File: pgfsysprotocol.code.tex 2023-01-15 v3.1.10 (3.1.10)
))
(/usr/local/texlive/2025basic/texmf-dist/tex/generic/pgf/basiclayer/pgfcore.cod
e.tex
Package: pgfcore 2023-01-15 v3.1.10 (3.1.10)

(/usr/local/texlive/2025basic/texmf-dist/tex/generic/pgf/math/pgfmath.code.tex
(/usr/local/texlive/2025basic/texmf-dist/tex/generic/pgf/math/pgfmathutil.code.
tex)
(/usr/local/texlive/2025basic/texmf-dist/tex/generic/pgf/math/pgfmathparser.cod
e.tex
\pgfmath@dimen=\dimen174
\pgfmath@count=\count292
\pgfmath@box=\box100
\pgfmath@toks=\toks27
\pgfmath@stack@operand=\toks28
\pgfmath@stack@operation=\toks29
)
(/usr/local/texlive/2025basic/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.
code.tex)
(/usr/local/texlive/2025basic/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.
basic.code.tex)
(/usr/local/texlive/2025basic/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.
trigonometric.code.tex)
(/usr/local/texlive/2025basic/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.
random.code.tex)
(/usr/local/texlive/2025basic/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.
comparison.code.tex)
(/usr/local/texlive/2025basic/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.
base.code.tex)
(/usr/local/texlive/2025basic/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.
round.code.tex)
(/usr/local/texlive/2025basic/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.
misc.code.tex)
(/usr/local/texlive/2025basic/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.
integerarithmetics.code.tex)
(/usr/local/texlive/2025basic/texmf-dist/tex/generic/pgf/math/pgfmathcalc.code.
tex)
(/usr/local/texlive/2025basic/texmf-dist/tex/generic/pgf/math/pgfmathfloat.code
.tex
\c@pgfmathroundto@lastzeros=\count293
))
(/usr/local/texlive/2025basic/texmf-dist/tex/generic/pgf/math/pgfint.code.tex)
(/usr/local/texlive/2025basic/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepoin
ts.code.tex
File: pgfcorepoints.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@picminx=\dimen175
\pgf@picmaxx=\dimen176
\pgf@picminy=\dimen177
\pgf@picmaxy=\dimen178
\pgf@pathminx=\dimen179
\pgf@pathmaxx=\dimen180
\pgf@pathminy=\dimen181
\pgf@pathmaxy=\dimen182
\pgf@xx=\dimen183
\pgf@xy=\dimen184
\pgf@yx=\dimen185
\pgf@yy=\dimen186
\pgf@zx=\dimen187
\pgf@zy=\dimen188
)
(/usr/local/texlive/2025basic/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepath
construct.code.tex
File: pgfcorepathconstruct.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@path@lastx=\dimen189
\pgf@path@lasty=\dimen190
)
(/usr/local/texlive/2025basic/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepath
usage.code.tex
File: pgfcorepathusage.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@shorten@end@additional=\dimen191
\pgf@shorten@start@additional=\dimen192
)
(/usr/local/texlive/2025basic/texmf-dist/tex/generic/pgf/basiclayer/pgfcorescop
es.code.tex
File: pgfcorescopes.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfpic=\box101
\pgf@hbox=\box102
\pgf@layerbox@main=\box103
\pgf@picture@serial@count=\count294
)
(/usr/local/texlive/2025basic/texmf-dist/tex/generic/pgf/basiclayer/pgfcoregrap
hicstate.code.tex
File: pgfcoregraphicstate.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgflinewidth=\dimen193
)
(/usr/local/texlive/2025basic/texmf-dist/tex/generic/pgf/basiclayer/pgfcoretran
sformations.code.tex
File: pgfcoretransformations.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@pt@x=\dimen194
\pgf@pt@y=\dimen195
\pgf@pt@temp=\dimen196
)
(/usr/local/texlive/2025basic/texmf-dist/tex/generic/pgf/basiclayer/pgfcorequic
k.code.tex
File: pgfcorequick.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(/usr/local/texlive/2025basic/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreobje
cts.code.tex
File: pgfcoreobjects.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(/usr/local/texlive/2025basic/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepath
processing.code.tex
File: pgfcorepathprocessing.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(/usr/local/texlive/2025basic/texmf-dist/tex/generic/pgf/basiclayer/pgfcorearro
ws.code.tex
File: pgfcorearrows.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfarrowsep=\dimen197
)
(/usr/local/texlive/2025basic/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreshad
e.code.tex
File: pgfcoreshade.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@max=\dimen198
\pgf@sys@shading@range@num=\count295
\pgf@shadingcount=\count296
)
(/usr/local/texlive/2025basic/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreimag
e.code.tex
File: pgfcoreimage.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(/usr/local/texlive/2025basic/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreexte
rnal.code.tex
File: pgfcoreexternal.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfexternal@startupbox=\box104
)
(/usr/local/texlive/2025basic/texmf-dist/tex/generic/pgf/basiclayer/pgfcorelaye
rs.code.tex
File: pgfcorelayers.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(/usr/local/texlive/2025basic/texmf-dist/tex/generic/pgf/basiclayer/pgfcoretran
sparency.code.tex
File: pgfcoretransparency.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(/usr/local/texlive/2025basic/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepatt
erns.code.tex
File: pgfcorepatterns.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(/usr/local/texlive/2025basic/texmf-dist/tex/generic/pgf/basiclayer/pgfcorerdf.
code.tex
File: pgfcorerdf.code.tex 2023-01-15 v3.1.10 (3.1.10)
)))
(/usr/local/texlive/2025basic/texmf-dist/tex/generic/pgf/modules/pgfmoduleshape
s.code.tex
File: pgfmoduleshapes.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfnodeparttextbox=\box105
)
(/usr/local/texlive/2025basic/texmf-dist/tex/generic/pgf/modules/pgfmoduleplot.
code.tex
File: pgfmoduleplot.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(/usr/local/texlive/2025basic/texmf-dist/tex/latex/pgf/compatibility/pgfcomp-ve
rsion-0-65.sty
Package: pgfcomp-version-0-65 2023-01-15 v3.1.10 (3.1.10)
\pgf@nodesepstart=\dimen199
\pgf@nodesepend=\dimen256
)
(/usr/local/texlive/2025basic/texmf-dist/tex/latex/pgf/compatibility/pgfcomp-ve
rsion-1-18.sty
Package: pgfcomp-version-1-18 2023-01-15 v3.1.10 (3.1.10)
))
(/usr/local/texlive/2025basic/texmf-dist/tex/latex/pgf/utilities/pgffor.sty
(/usr/local/texlive/2025basic/texmf-dist/tex/latex/pgf/utilities/pgfkeys.sty
(/usr/local/texlive/2025basic/texmf-dist/tex/generic/pgf/utilities/pgfkeys.code
.tex)) (/usr/local/texlive/2025basic/texmf-dist/tex/latex/pgf/math/pgfmath.sty
(/usr/local/texlive/2025basic/texmf-dist/tex/generic/pgf/math/pgfmath.code.tex)
)
(/usr/local/texlive/2025basic/texmf-dist/tex/generic/pgf/utilities/pgffor.code.
tex
Package: pgffor 2023-01-15 v3.1.10 (3.1.10)
\pgffor@iter=\dimen257
\pgffor@skip=\dimen258
\pgffor@stack=\toks30
\pgffor@toks=\toks31
))
(/usr/local/texlive/2025basic/texmf-dist/tex/generic/pgf/frontendlayer/tikz/tik
z.code.tex
Package: tikz 2023-01-15 v3.1.10 (3.1.10)

(/usr/local/texlive/2025basic/texmf-dist/tex/generic/pgf/libraries/pgflibrarypl
othandlers.code.tex
File: pgflibraryplothandlers.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@plot@mark@count=\count297
\pgfplotmarksize=\dimen259
)
\tikz@lastx=\dimen260
\tikz@lasty=\dimen261
\tikz@lastxsaved=\dimen262
\tikz@lastysaved=\dimen263
\tikz@lastmovetox=\dimen264
\tikz@lastmovetoy=\dimen265
\tikzleveldistance=\dimen266
\tikzsiblingdistance=\dimen267
\tikz@figbox=\box106
\tikz@figbox@bg=\box107
\tikz@tempbox=\box108
\tikz@tempbox@bg=\box109
\tikztreelevel=\count298
\tikznumberofchildren=\count299
\tikznumberofcurrentchild=\count300
\tikz@fig@count=\count301

(/usr/local/texlive/2025basic/texmf-dist/tex/generic/pgf/modules/pgfmodulematri
x.code.tex
File: pgfmodulematrix.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfmatrixcurrentrow=\count302
\pgfmatrixcurrentcolumn=\count303
\pgf@matrix@numberofcolumns=\count304
)
\tikz@expandcount=\count305

(/usr/local/texlive/2025basic/texmf-dist/tex/generic/pgf/frontendlayer/tikz/lib
raries/tikzlibrarytopaths.code.tex
File: tikzlibrarytopaths.code.tex 2023-01-15 v3.1.10 (3.1.10)
)))
(/usr/local/texlive/2025basic/texmf-dist/tex/latex/tools/verbatim.sty
Package: verbatim 2024-01-22 v1.5x LaTeX2e package for verbatim enhancements
\every@verbatim=\toks32
\verbatim@line=\toks33
\verbatim@in@stream=\read3
)
(/usr/local/texlive/2025basic/texmf-dist/tex/latex/environ/environ.sty
Package: environ 2014/05/04 v0.3 A new way to define environments

(/usr/local/texlive/2025basic/texmf-dist/tex/latex/trimspaces/trimspaces.sty
Package: trimspaces 2009/09/17 v1.1 Trim spaces around a token list
)
\@envbody=\toks34
)
(/usr/local/texlive/2025basic/texmf-dist/tex/latex/etoolbox/etoolbox.sty
Package: etoolbox 2025/02/11 v2.5l e-TeX tools for LaTeX (JAW)
\etb@tempcnta=\count306
)
\tcb@titlebox=\box110
\tcb@upperbox=\box111
\tcb@lowerbox=\box112
\tcb@phantombox=\box113
\c@tcbbreakpart=\count307
\c@tcblayer=\count308
\c@tcolorbox@number=\count309
\l__tcobox_tmpa_box=\box114
\l__tcobox_tmpa_dim=\dimen268
\tcb@temp=\box115
\tcb@temp=\box116
\tcb@temp=\box117
\tcb@temp=\box118
)
(/usr/local/texlive/2025basic/texmf-dist/tex/latex/titlesec/titlesec.sty
Package: titlesec 2025/01/04 v2.17 Sectioning titles
\ttl@box=\box119
\beforetitleunit=\skip54
\aftertitleunit=\skip55
\ttl@plus=\dimen269
\ttl@minus=\dimen270
\ttl@toksa=\toks35
\titlewidth=\dimen271
\titlewidthlast=\dimen272
\titlewidthfirst=\dimen273
)
(/usr/local/texlive/2025basic/texmf-dist/tex/latex/fancyhdr/fancyhdr.sty
Package: fancyhdr 2025/02/07 v5.2 Extensive control of page headers and footers

\f@nch@headwidth=\skip56
\f@nch@offset@elh=\skip57
\f@nch@offset@erh=\skip58
\f@nch@offset@olh=\skip59
\f@nch@offset@orh=\skip60
\f@nch@offset@elf=\skip61
\f@nch@offset@erf=\skip62
\f@nch@offset@olf=\skip63
\f@nch@offset@orf=\skip64
\f@nch@height=\skip65
\f@nch@footalignment=\skip66
\f@nch@widthL=\skip67
\f@nch@widthC=\skip68
\f@nch@widthR=\skip69
\@temptokenb=\toks36
)
(/usr/local/texlive/2025basic/texmf-dist/tex/latex/l3backend/l3backend-pdftex.d
ef
File: l3backend-pdftex.def 2024-05-08 L3 backend support: PDF output (pdfTeX)
\l__color_backend_stack_int=\count310
\l__pdf_internal_box=\box120
)
No file study_guide.aux.
\openout1 = `study_guide.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 45.
LaTeX Font Info:    ... okay on input line 45.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 45.
LaTeX Font Info:    ... okay on input line 45.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 45.
LaTeX Font Info:    ... okay on input line 45.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 45.
LaTeX Font Info:    ... okay on input line 45.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 45.
LaTeX Font Info:    ... okay on input line 45.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 45.
LaTeX Font Info:    ... okay on input line 45.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 45.
LaTeX Font Info:    ... okay on input line 45.
*geometry* driver: auto-detecting
*geometry* detected driver: pdftex
*geometry* verbose mode - [ preamble ] result:
* driver: pdftex
* paper: letterpaper
* layout: <same size as paper>
* layoutoffset:(h,v)=(0.0pt,0.0pt)
* modes: 
* h-part:(L,W,R)=(36.135pt, 542.02501pt, 36.135pt)
* v-part:(T,H,B)=(36.135pt, 722.7pt, 36.135pt)
* \paperwidth=614.295pt
* \paperheight=794.96999pt
* \textwidth=542.02501pt
* \textheight=722.7pt
* \oddsidemargin=-36.135pt
* \evensidemargin=-36.135pt
* \topmargin=-73.135pt
* \headheight=12.0pt
* \headsep=25.0pt
* \topskip=10.0pt
* \footskip=30.0pt
* \marginparwidth=65.0pt
* \marginparsep=11.0pt
* \columnsep=10.0pt
* \skip\footins=9.0pt plus 4.0pt minus 2.0pt
* \hoffset=0.0pt
* \voffset=0.0pt
* \mag=1000
* \@twocolumnfalse
* \@twosidefalse
* \@mparswitchfalse
* \@reversemarginfalse
* (1in=72.27pt=25.4mm, 1cm=28.453pt)

(/usr/local/texlive/2025basic/texmf-dist/tex/context/base/mkii/supp-pdf.mkii
[Loading MPS to PDF converter (version 2006.09.02).]
\scratchcounter=\count311
\scratchdimen=\dimen274
\scratchbox=\box121
\nofMPsegments=\count312
\nofMParguments=\count313
\everyMPshowfont=\toks37
\MPscratchCnt=\count314
\MPscratchDim=\dimen275
\MPnumerator=\count315
\makeMPintoPDFobject=\count316
\everyMPtoPDFconversion=\toks38
)
(/usr/local/texlive/2025basic/texmf-dist/tex/latex/epstopdf-pkg/epstopdf-base.s
ty
Package: epstopdf-base 2020-01-24 v2.11 Base part for package epstopdf
Package epstopdf-base Info: Redefining graphics rule for `.eps' on input line 4
85.

(/usr/local/texlive/2025basic/texmf-dist/tex/latex/latexconfig/epstopdf-sys.cfg
File: epstopdf-sys.cfg 2010/07/13 v1.3 Configuration of (r)epstopdf for TeX Liv
e
))

! Package pgfkeys Error: I do not know the key '/tcb/title style', to which you
 passed 'colback=gray!20', and I am going to ignore it. Perhaps you misspelled 
it.

See the pgfkeys package documentation for exp