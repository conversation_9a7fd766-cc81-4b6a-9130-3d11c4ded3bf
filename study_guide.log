This is pdfTeX, Version 3.141592653-2.6-1.40.27 (TeX Live 2025) (preloaded format=pdflatex 2025.5.23)  25 MAY 2025 15:48
entering extended mode
 restricted \write18 enabled.
 %&-line parsing enabled.
**study_guide.tex
(./study_guide.tex
LaTeX2e <2024-11-01> patch level 2
L3 programming layer <2025-01-18>
(/usr/local/texlive/2025basic/texmf-dist/tex/latex/base/article.cls
Document Class: article 2024/06/29 v1.4n Standard LaTeX document class
(/usr/local/texlive/2025basic/texmf-dist/tex/latex/base/size10.clo
File: size10.clo 2024/06/29 v1.4n Standard LaTeX file (size option)
)
\c@part=\count196
\c@section=\count197
\c@subsection=\count198
\c@subsubsection=\count199
\c@paragraph=\count266
\c@subparagraph=\count267
\c@figure=\count268
\c@table=\count269
\abovecaptionskip=\skip49
\belowcaptionskip=\skip50
\bibindent=\dimen141
)
(/usr/local/texlive/2025basic/texmf-dist/tex/latex/geometry/geometry.sty
Package: geometry 2020/01/02 v5.9 Page Geometry

(/usr/local/texlive/2025basic/texmf-dist/tex/latex/graphics/keyval.sty
Package: keyval 2022/05/29 v1.15 key=value parser (DPC)
\KV@toks@=\toks17
)
(/usr/local/texlive/2025basic/texmf-dist/tex/generic/iftex/ifvtex.sty
Package: ifvtex 2019/10/25 v1.7 ifvtex legacy package. Use iftex instead.

(/usr/local/texlive/2025basic/texmf-dist/tex/generic/iftex/iftex.sty
Package: iftex 2024/12/12 v1.0g TeX engine tests
))
\Gm@cnth=\count270
\Gm@cntv=\count271
\c@Gm@tempcnt=\count272
\Gm@bindingoffset=\dimen142
\Gm@wd@mp=\dimen143
\Gm@odd@mp=\dimen144
\Gm@even@mp=\dimen145
\Gm@layoutwidth=\dimen146
\Gm@layoutheight=\dimen147
\Gm@layouthoffset=\dimen148
\Gm@layoutvoffset=\dimen149
\Gm@dimlist=\toks18
)
(/usr/local/texlive/2025basic/texmf-dist/tex/latex/tools/multicol.sty
Package: multicol 2024/09/14 v1.9i multicolumn formatting (FMi)
\c@tracingmulticols=\count273
\mult@box=\box52
\multicol@leftmargin=\dimen150
\c@unbalance=\count274
\c@collectmore=\count275
\doublecol@number=\count276
\multicoltolerance=\count277
\multicolpretolerance=\count278
\full@width=\dimen151
\page@free=\dimen152
\premulticols=\dimen153
\postmulticols=\dimen154
\multicolsep=\skip51
\multicolbaselineskip=\skip52
\partial@page=\box53
\last@line=\box54
\mc@boxedresult=\box55
\maxbalancingoverflow=\dimen155
\mult@rightbox=\box56
\mult@grightbox=\box57
\mult@firstbox=\box58
\mult@gfirstbox=\box59
\@tempa=\box60
\@tempa=\box61
\@tempa=\box62
\@tempa=\box63
\@tempa=\box64
\@tempa=\box65
\@tempa=\box66
\@tempa=\box67
\@tempa=\box68
\@tempa=\box69
\@tempa=\box70
\@tempa=\box71
\@tempa=\box72
\@tempa=\box73
\@tempa=\box74
\@tempa=\box75
\@tempa=\box76
\@tempa=\box77
\@tempa=\box78
\@tempa=\box79
\@tempa=\box80
\@tempa=\box81
\@tempa=\box82
\@tempa=\box83
\@tempa=\box84
\@tempa=\box85
\@tempa=\box86
\@tempa=\box87
\@tempa=\box88
\@tempa=\box89
\@tempa=\box90
\@tempa=\box91
\@tempa=\box92
\@tempa=\box93
\@tempa=\box94
\@tempa=\box95
\c@minrows=\count279
\c@columnbadness=\count280
\c@finalcolumnbadness=\count281
\last@try=\dimen156
\multicolovershoot=\dimen157
\multicolundershoot=\dimen158
\mult@nat@firstbox=\box96
\colbreak@box=\box97
\mc@col@check@num=\count282
)
(/usr/local/texlive/2025basic/texmf-dist/tex/latex/enumitem/enumitem.sty
Package: enumitem 2025/02/06 v3.11 Customized lists
\labelindent=\skip53
\enit@outerparindent=\dimen159
\enit@toks=\toks19
\enit@inbox=\box98
\enit@count@id=\count283
\enitdp@description=\count284
)
(/usr/local/texlive/2025basic/texmf-dist/tex/latex/xcolor/xcolor.sty
Package: xcolor 2024/09/29 v3.02 LaTeX color extensions (UK)

(/usr/local/texlive/2025basic/texmf-dist/tex/latex/graphics-cfg/color.cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package xcolor Info: Driver file: pdftex.def on input line 274.

(/usr/local/texlive/2025basic/texmf-dist/tex/latex/graphics-def/pdftex.def
File: pdftex.def 2024/04/13 v1.2c Graphics/color driver for pdftex
)
(/usr/local/texlive/2025basic/texmf-dist/tex/latex/graphics/mathcolor.ltx)
Package xcolor Info: Model `cmy' substituted by `cmy0' on input line 1349.
Package xcolor Info: Model `hsb' substituted by `rgb' on input line 1353.
Package xcolor Info